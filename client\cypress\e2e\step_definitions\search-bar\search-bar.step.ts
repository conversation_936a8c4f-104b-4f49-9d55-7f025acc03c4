import { Then, Given, Before } from '@badeball/cypress-cucumber-preprocessor';
import { mainPage } from '../../../pages/mainPage';
import '../activeTab/activeTab.step';

Before({ tags: '@search' }, () => {
  cy.LoginLandingPage();
  cy.intercept('GET', /api\/v1\/events\/\?sortBy/).as('fetchEvent');
});

Given('The user is on Event Screen', () => {
  mainPage.visit();
});

Then('The user should see no results when the search yields no matches', () => {
  mainPage.verifyNoSearchResultsFound();
});

Given('The user navigates to the Files tab after searching', () => {
  mainPage.visit();
  mainPage.clickTab('Files');
});
