Feature: File Screen

  Background:
    Given The user is on File Screen

  @e2e @file-screen
  Scenario: Verify user can see file detail when clicking on a file name
    When The user selects the "file" named "4s testing tdo.mp4"
    Then The following "file" details should be visible:
      | Field              | Expected Value                 |
      | File Name          | 4s testing tdo.mp4             |
      | Upload Date        | Uploaded: 5/29/2025, 11:34 AM  |
      | File Creator       | File Uploaded by <PERSON><PERSON>   |
      | File Size          | File Size: 1.5 Mb              |
      | GPS Location       | File GPS Location: Unavailable |
      | View File Button   | View File                      |
      | Delete File Button | Delete File                    |

  @e2e @file-screen
  Scenario: Verify format of the event time
    Then "file" Time has format: "M/D/YYYY, h:m AM/PM"

  @e2e @file-screen
  Scenario: Verify user can edit file name
    When The user selects the "file" named "4s testing tdo.mp4"
    And The user clicks the "file" name located on the right side
    And The user changes "file" name to "renamed file.mp4"
    Then The name of the "file" should be updated to "renamed file.mp4"
    And The user clicks the "file" name located on the right side
    And The user changes "file" name to "4s testing tdo.mp4"
    Then The name of the "file" should be updated to "4s testing tdo.mp4"

  @e2e @file-screen @skip
  Scenario: Verify user can delete a file successfully

  @e2e @file-screen
  Scenario Outline: Verify user can sort the file table columns by <ColumnName> <SortedBy>
    When The user clicks on the "<ColumnName>" column header for "file"
    Then The "file" table should be sorted by "<ColumnName>" in "<SortedBy>" order

    Examples:
      | ColumnName  | SortedBy |
      | File Name   | z-a      |
      | File Name   | a-z      |
      | Upload Date | z-a      |
      | Upload Date | a-z      |

  @e2e @file-screen
  Scenario: Verify results per page functionality
    Then The user should see the "Results Per Page" label
    When The user changes the results per page and verifies the following options:
      | PerPage |
      | 100     |
      | 10      |
      | 50      |

  @e2e @file-screen
  Scenario: Verify user can move between pages
    Then The user should see the initial pagination state
    When The user navigates to the "next" page
    Then The pagination should update for the next page
    When The user navigates to the "previous" page
    Then The pagination should return to the initial state
